
export const generateDummyData = (year,months) => {
  return months.map((month, idx) => {
    const base = year === 2024 ? 1.2 : year === 2023 ? 1.0 : 0.8;
    const seasonal = Math.sin(idx * Math.PI / 6) * 0.3 + 1;
    const newInv = Math.floor((50 + Math.random() * 100) * base * seasonal);
    const extracted = Math.floor(newInv * (0.85 + Math.random() * 0.1));
    const validated = Math.floor(extracted * (0.80 + Math.random() * 0.15));
    const exported = Math.floor(validated * (0.75 + Math.random() * 0.2));
    const completed = Math.floor(exported * (0.90 + Math.random() * 0.1));
    
    return {
      id: `${year}-${idx + 1}`,
      month,
      new: newInv,
      extracted,
      validated,
      exported,
      completed,
      total: newInv + extracted + validated + exported + completed,
      monthNumber: idx + 1,
      year
    };
  });
};