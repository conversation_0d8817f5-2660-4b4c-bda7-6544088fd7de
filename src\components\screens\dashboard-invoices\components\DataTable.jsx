
import TableHeader from "./TableHeader";

const DataTable = ({
  columns,
  data,
  itemsPerPage = 20,
  maxPages = 50,
  totals = {},
  maxHeight = '600px'
}) => {
  const maxItems = itemsPerPage * maxPages;
  const limitedData = data.slice(0, maxItems);

  return (
    <div className="bg-white rounded-xl shadow-xs border border-gray-100 overflow-hidden">
      <div className="overflow-auto" style={{ maxHeight }}>
        <table className="min-w-full divide-y divide-gray-100">
          <TableHeader columns={columns} />
          <tbody className="bg-white divide-y divide-gray-100">
            <tr className="bg-gray-50 border-b border-gray-200 sticky top-[52px] z-10">
              {columns.map((col, colIdx) => (
                <td
                  key={`total-${col.key}`}
                  className={`px-6 py-4 whitespace-nowrap ${colIdx === 0
                      ? 'text-gray-700 text-left min-w-[140px]'
                      : 'text-center min-w-[100px]'
                    }`}
                >
                  {colIdx === 0 ? (
                    <span className="text-lg">Total</span>
                  ) : (
                    <div className="text-lg">
                      {totals[col.key]?.toLocaleString() || '0'}
                    </div>
                  )}
                </td>
              ))}
            </tr>
            {limitedData.map((row) => (
              <tr
                key={row.id}
                className="hover:bg-gray-50 transition-colors"
              >
                {columns.map((col, colIdx) => (
                  <td
                    key={col.key}
                    className={`px-6 py-4 whitespace-nowrap ${colIdx === 0
                        ? 'font-medium text-gray-600 text-left min-w-[140px]'
                        : 'text-center min-w-[100px]'
                      }`}
                  >
                    {colIdx === 0 ? (
                      <span className="text-lg">{row[col.key]}</span>
                    ) : (
                      <div className="text-lg">
                        {row[col.key]?.toLocaleString() || '0'}
                      </div>
                    )}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default DataTable;