
import React, { useState, useMemo } from 'react';
import  TableAction  from './components/TableAction.jsx';
import  DataTable  from './components/DataTable.jsx';
import LayoutWrapper from "../../generic-components/LayoutWrapper.jsx";
import { generateDummyData } from './utils.js';

const values=[
  'Greater than 10L','5L-10L','1L-5L','10K-1L','0- 10K',
]
const TABLE_COLUMNS = [
  { key: 'month', label: '' },
  { key: 'new', label: 'New' },
  { key: 'extracted', label: 'Extracted' },
  { key: 'validated', label: 'Validated' },
  { key: 'exported', label: 'Exported' },
  { key: 'completed', label: 'Completed' },
  { key: 'total', label: 'Total' },
];
const SUMMARY_COLUMNS = TABLE_COLUMNS.slice(1);

const InvoiceValue = () => {
  const [year, setYear] = useState(2024);
  const [page, setPage] = useState(1);
  const perPage = 6;

  const yearRanges = [
    { value: 2022, label: '2022-23' },
    { value: 2023, label: '2023-24' },
    { value: 2024, label: '2024-25' }
  ];
  const rawData = useMemo(() => generateDummyData(year,values), [year]);
  const totals = useMemo(() => {
    const initialTotals = {};
    SUMMARY_COLUMNS.forEach(col => {
      initialTotals[col.key] = 0;
    });

    return rawData.reduce((acc, row) => {
      SUMMARY_COLUMNS.forEach(col => {
        acc[col.key] += row[col.key];
      });
      return acc;
    }, initialTotals);
  }, [rawData]);

  const totalInvoiceCount = totals.total || 0;
  return (
    <LayoutWrapper>
      <div className="min-h-screen bg-gray-50">
        <div>
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 mb-6">
            <div className="flex items-center ">
              <h1 className="text-2xl sm:text-1xl  text-gray-700">Invoices By Value</h1>
            </div>
            <TableAction
              year={year}
              setYear={setYear}
              yearRanges={yearRanges}
              totalInvoiceCount={totalInvoiceCount}
              setPage={setPage}
            />
          </div>
          <DataTable
            columns={TABLE_COLUMNS}
            data={rawData}
            totals={totals}
            currentPage={page}
            itemsPerPage={perPage}
            onPageChange={setPage}
          />
        </div>
      </div>
    </LayoutWrapper>
  );
};

export default InvoiceValue;
