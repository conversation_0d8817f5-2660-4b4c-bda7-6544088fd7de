import { Calendar, Hash } from 'lucide-react';

const TableAction = ({
  totalInvoiceCount,
  year,
  setYear,
  yearRanges,
  setPage
}) => {
  return (
    <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-3 sm:space-y-0 sm:space-x-4 w-full sm:w-auto">
      <div className="flex items-center space-x-2  px-3 py-2 rounded-lg shadow-sm border  w-full sm:w-auto bg-accent1-bg border-accent1-border">
        <Hash className="h-4 w-4 text-gray-800" />
        <span className="text-lg  text-gray-900 ">Count:</span>
        <span className="text-lg  text-gray-900 ">{totalInvoiceCount.toLocaleString()}</span>
      </div>
      <div className="flex items-center space-x-3 w-full sm:w-auto">
        <Calendar className="h-5 w-5 text-gray-600" />
        <select
          value={year}
          onChange={(e) => {
            setYear(+e.target.value);
            setPage(1);
          }}
          className="px-3 py-1.5 sm:px-4 sm:py-2 border border-accent1-border rounded-lg  shadow-sm focus:ring-2 focus:ring-gray-500 w-full sm:w-auto bg-accent1-bg text-lg"
        >
          {yearRanges.map((yr) => (
            <option key={yr.value} value={yr.value}>
              {yr.label}
            </option>
          ))}
        </select>
      </div>
    </div>
  );
};

export default TableAction;