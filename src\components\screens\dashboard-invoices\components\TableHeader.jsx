const TableHeader = ({ columns }) => (
  <thead className="bg-gray-50 sticky top-0 z-10">
    <tr>
      {columns.map((column, idx) => (
        <th
          key={idx}
          className={`px-6 py-4 text-lg font-medium text-gray-900 tracking-wider
            ${idx === 0 ? 'text-left rounded-tl-xl min-w-[140px]' : 'text-center min-w-[100px]'} 
            ${idx === columns.length - 1 ? 'rounded-tr-xl' : ''}`}
        >
          {column.label}
        </th>
      ))}
    </tr>
  </thead>
);

export default TableHeader;