export function getFieldAlertObject(sectionObj, field, fallbackField) {
  const errorField =
    sectionObj?.error?.[field] ||
    (fallbackField && sectionObj?.error?.[fallbackField]);
  if (errorField) {
    return {
      error: errorField.short_message,
      longStatusMsg: errorField.long_message,
    };
  }

  const warningField =
    sectionObj?.warning?.[field] ||
    (fallbackField && sectionObj?.warning?.[fallbackField]);
  if (warningField) {
    return {
      warning: warningField.short_message,
      longStatusMsg: warningField.long_message,
    };
  }

  return null;
}

export function recommendedOptionsMapper(data, field) {
  if (!data?.recommended_fields || !Array.isArray(data.recommended_fields)) {
    return [];
  }

  return data.recommended_fields.map((item, index) => ({
    label: item[field] ?? "Unknown",
    value: item[field] ?? "Unknown",
    key: index,
    similarity_score: item?.similarity_score,
  }));
}

export function mapAiInvoiceStatus(status) {
  status = Number(status);
  if (!status && status !== 0) return null;
  switch (status) {
    case -1:
      return "In Process";
    case 0:
      return "New";
    case 1:
    case 2:
      return "Extracted";
    case 3:
      return "Validated";
    case 4:
      return "Exported";
    case 5:
      return "Completed";
    case 6:
      return "Duplicate";
    default:
      return "Failed";
  }
}

export function isSuggestion(obj) {
  return (
    Array.isArray(obj) &&
    typeof obj[0] === "object" &&
    obj[0] !== null &&
    !("similarity_score" in obj[0])
  );
}

export function getSuggestion(obj) {
  if (
    Array.isArray(obj) &&
    typeof obj[0] === "object" &&
    obj[0] !== null &&
    !("similarity_score" in obj[0])
  ) {
    return obj[0];
  }
  return null;
}

/** Returns list of sections which has error  */
export function getSectionsWithErrors(data, keysToSkip = []) {
  const sectionsWithErrors = [];

  for (const [key, value] of Object.entries(data)) {
    if (
      value !== null &&
      typeof value === "object" &&
      !keysToSkip.includes(key)
    ) {
      if (Array.isArray(value)) {
        const hasErrorItem = value.some(
          (item) =>
            item !== null &&
            typeof item === "object" &&
            "error" in item &&
            item.error !== null &&
            item.error !== undefined
        );
        if (hasErrorItem) {
          sectionsWithErrors.push(key);
        }
      } else {
        if (
          "error" in value &&
          value.error !== null &&
          value.error !== undefined
        ) {
          sectionsWithErrors.push(key);
        }
      }
    }
  }

  return sectionsWithErrors;
}

export function checkSameState(formData) {
  const buyerCode = String(formData?.bill_to_details?.buyer_state_code)?.trim();
  const supplierCode = String(
    formData?.supplier_details?.supplier_state_code
  )?.trim();

  if (buyerCode && supplierCode)
    return Number(buyerCode) === Number(supplierCode);

  const buyerName = formData?.bill_to_details?.buyer_state_name
    ?.trim()
    ?.toLowerCase();
  const supplierName = formData?.supplier_details?.supplier_state_name
    ?.trim()
    ?.toLowerCase();

  if (buyerName && supplierName) return buyerName === supplierName;

  return false;
}
