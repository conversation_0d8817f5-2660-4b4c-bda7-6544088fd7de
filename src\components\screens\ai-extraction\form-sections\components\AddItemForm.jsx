import React, { useEffect, useState, useCallback } from "react";
import { <PERSON><PERSON>, Button, Form, Dropdown } from "semantic-ui-react";
import { NumericFormat } from "react-number-format";
import { Formik, Field } from "formik";
import * as yup from "yup";
import { useAuth } from "../../../../../contexts/AuthContext";
import apiClient from "../../../../services/apiClient";
import { resturls } from "../../../../utils/apiurls";
import { addItem } from "../../../../services/aiServices";

// Yup validation schema
const validationSchema = yup.object().shape({
  name: yup.string().required("Name is required"),
  rate: yup
    .string()
    .required("Rate is required")
    .test("is-number", "Rate must be a valid number", (value) => {
      return value && !isNaN(parseFloat(value)) && parseFloat(value) > 0;
    }),
  account_id: yup.string().required("Account is required"),
  tax_id: yup.string().required("Tax is required"),
  hsn_or_sac: yup.string().required("HSN/SAC is required"),
  description: yup.string(),
  item_type: yup.string().required("Item type is required"),
  unit: yup.string(),
  is_taxable: yup.boolean(),
  purchase_rate: yup
    .string()
    .test("is-number", "Purchase rate must be a valid number", (value) => {
      return !value || (!isNaN(parseFloat(value)) && parseFloat(value) > 0);
    }),
  product_type: yup.string().required("Product type is required"),
});

// Item type options
const itemTypeOptions = [
  { key: "sales", text: "Sales", value: "sales" },
  { key: "purchase", text: "Purchase", value: "purchase" },
  {
    key: "sales_and_purchase",
    text: "Sales and Purchase",
    value: "sales_and_purchase",
  },
];

// Product type options
const productTypeOptions = [
  { key: "goods", text: "Goods", value: "goods" },
  { key: "services", text: "Services", value: "services" },
];

// Initial form values
const initialValues = {
  name: "",
  rate: "",
  description: "",
  account_id: "",
  tax_id: "",
  item_type: "sales",
  unit: "",
  hsn_or_sac: "",
  is_taxable: true,
  purchase_rate: "",
  product_type: "goods",
};

function AddItemForm({ open, onClose, onSave = null }) {
  const { globSelectedBusiness } = useAuth();
  const businessId = globSelectedBusiness?.business_id;

  const [accounts, setAccounts] = useState([]);
  const [taxes, setTaxes] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  // Fetch accounts from API
  const fetchAccounts = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await apiClient.get(
        `${resturls.leadgerscreenitems}?business_id=${businessId}`
      );
      const accountsList = response.data.map((account) => ({
        key: account.id,
        text: account.ledger_name || account.name,
        value: account.id,
      }));
      setAccounts(accountsList);
    } catch (error) {
      console.error("Error fetching accounts:", error);
    } finally {
      setIsLoading(false);
    }
  }, [businessId]);

  // Fetch taxes from API
  const fetchTaxes = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await apiClient.get(
        `${resturls.TaxledgersItems}?business_id=${businessId}`
      );
      const taxesList = response.data.map((tax) => ({
        key: tax.id,
        text: `${tax.ledger} (${tax.tax_rate}%)`,
        value: tax.id,
      }));
      setTaxes(taxesList);
    } catch (error) {
      console.error("Error fetching taxes:", error);
    } finally {
      setIsLoading(false);
    }
  }, [businessId]);

  // Fetch accounts and taxes on component mount
  useEffect(() => {
    if (open && businessId) {
      fetchAccounts();
      fetchTaxes();
    }
  }, [open, businessId, fetchAccounts, fetchTaxes]);

  // Handle form submission
  const handleFormSubmit = (values, { resetForm }) => {
    // addItem(organization_id, values).then((res) => {
    //   console.log("res ::: ", res);
    // });
    onSave && onSave(values);
    resetForm();
    onClose && onClose();
  };

  return (
    <Modal
      open={open}
      onClose={() => onClose && onClose()}
      size="small"
      dimmer="blurring"
    >
      <Modal.Header>Add New Item</Modal.Header>
      <Modal.Content>
        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={handleFormSubmit}
          enableReinitialize
          validateOnMount={true}
        >
          {({
            values,
            errors,
            touched,
            handleSubmit,
            setFieldValue,
            isValid,
          }) => {
            // Check if required fields are filled
            const isFormReady =
              values.name &&
              values.rate &&
              values.account_id &&
              values.tax_id &&
              values.hsn_or_sac &&
              isValid;

            return (
              <>
                <Form
                  loading={isLoading}
                  onSubmit={handleSubmit}
                  id="add-item-form"
                >
                  {/* First row: Name, Rate, HSN/SAC, Unit */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-2">
                    <Form.Field
                      required
                      error={!!(errors.name && touched.name)}
                    >
                      <label>Name</label>
                      <Field name="name">
                        {({ field }) => (
                          <Form.Input
                            placeholder="Enter item name"
                            {...field}
                          />
                        )}
                      </Field>
                    </Form.Field>

                    <Form.Field
                      required
                      error={!!(errors.rate && touched.rate)}
                    >
                      <label>Rate</label>
                      <Field name="rate">
                        {({ field }) => (
                          <NumericFormat
                            customInput={Form.Input}
                            thousandSeparator={true}
                            decimalScale={2}
                            fixedDecimalScale
                            placeholder="0.00"
                            value={field.value}
                            onValueChange={(values) =>
                              setFieldValue("rate", values.value)
                            }
                          />
                        )}
                      </Field>
                    </Form.Field>

                    <Form.Field
                      required
                      error={!!(errors.hsn_or_sac && touched.hsn_or_sac)}
                    >
                      <label>HSN/SAC</label>
                      <Field name="hsn_or_sac">
                        {({ field }) => (
                          <Form.Input
                            placeholder="Enter HSN/SAC code"
                            {...field}
                          />
                        )}
                      </Field>
                    </Form.Field>

                    <Form.Field>
                      <label>Unit</label>
                      <Field name="unit">
                        {({ field }) => (
                          <Form.Input
                            placeholder="e.g., pcs, kg, m"
                            {...field}
                          />
                        )}
                      </Field>
                    </Form.Field>
                  </div>

                  {/* Second row: Account, Tax, Item Type, Product Type */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    <Form.Field
                      required
                      error={!!(errors.account_id && touched.account_id)}
                    >
                      <label>Account</label>
                      <Field name="account_id">
                        {({ field }) => (
                          <Dropdown
                            placeholder="Select account"
                            fluid
                            search
                            selection
                            options={accounts}
                            value={field.value}
                            onChange={(e, { value }) =>
                              setFieldValue("account_id", value)
                            }
                            loading={isLoading}
                          />
                        )}
                      </Field>
                    </Form.Field>

                    <Form.Field
                      required
                      error={!!(errors.tax_id && touched.tax_id)}
                    >
                      <label>Tax</label>
                      <Field name="tax_id">
                        {({ field }) => (
                          <Dropdown
                            placeholder="Select tax"
                            fluid
                            search
                            selection
                            options={taxes}
                            value={field.value}
                            onChange={(e, { value }) =>
                              setFieldValue("tax_id", value)
                            }
                            loading={isLoading}
                          />
                        )}
                      </Field>
                    </Form.Field>

                    <Form.Field>
                      <label>Item Type</label>
                      <Field name="item_type">
                        {({ field }) => (
                          <Dropdown
                            placeholder="Select item type"
                            fluid
                            selection
                            options={itemTypeOptions}
                            value={field.value}
                            onChange={(e, { value }) =>
                              setFieldValue("item_type", value)
                            }
                          />
                        )}
                      </Field>
                    </Form.Field>

                    <Form.Field>
                      <label>Product Type</label>
                      <Field name="product_type">
                        {({ field }) => (
                          <Dropdown
                            placeholder="Select product type"
                            fluid
                            selection
                            options={productTypeOptions}
                            value={field.value}
                            onChange={(e, { value }) =>
                              setFieldValue("product_type", value)
                            }
                          />
                        )}
                      </Field>
                    </Form.Field>
                  </div>

                  {/* Description row */}
                  <div className="mt-4">
                    <Form.Field>
                      <label>Description</label>
                      <Field name="description">
                        {({ field }) => (
                          <Form.TextArea
                            placeholder="Enter description"
                            {...field}
                            rows={3}
                          />
                        )}
                      </Field>
                    </Form.Field>
                  </div>

                  {/* Conditional Purchase Rate field */}
                  {(values.item_type === "purchase" ||
                    values.item_type === "sales_and_purchase") && (
                    <div className="mt-4">
                      <Form.Field>
                        <label>Purchase Rate</label>
                        <Field name="purchase_rate">
                          {({ field }) => (
                            <NumericFormat
                              customInput={Form.Input}
                              thousandSeparator={true}
                              decimalScale={2}
                              fixedDecimalScale
                              placeholder="0.00"
                              value={field.value}
                              onValueChange={(values) =>
                                setFieldValue("purchase_rate", values.value)
                              }
                            />
                          )}
                        </Field>
                      </Form.Field>
                    </div>
                  )}

                  {/* Is Taxable checkbox */}
                  <div className="mt-4">
                    <Form.Field>
                      <Field name="is_taxable">
                        {({ field }) => (
                          <Form.Checkbox
                            label="Is Taxable"
                            checked={field.value}
                            onChange={(e, { checked }) =>
                              setFieldValue("is_taxable", checked)
                            }
                          />
                        )}
                      </Field>
                    </Form.Field>
                  </div>
                </Form>
                <div className="flex justify-end gap-2">
                  <Button onClick={() => onClose && onClose()}>Cancel</Button>
                  <Button
                    onClick={() => handleSubmit()}
                    className="!bg-accent2 !text-white hover:!bg-accent2-hover"
                    disabled={!isFormReady}
                    type="submit"
                  >
                    Add Item
                  </Button>
                </div>
              </>
            );
          }}
        </Formik>
      </Modal.Content>
    </Modal>
  );
}

export default AddItemForm;
